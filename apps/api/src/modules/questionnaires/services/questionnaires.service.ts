import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import {
  QuestionnaireRepository,
  SessionContext,
  Questionnaire,
  QuestionnaireFilters,
  Question,
  QuestionnaireWithQuestions,
} from '@askinfosec/database-drizzle';
import { ContractsV1Questionnaires } from '@askinfosec/types';

export interface GetQuestionnairesFilters {
  search?: string;
  status?: string[];
  vendorId?: string[];
  scopeId?: string[];
  createdBy?: string[];
  assigned?: string[];
  isAssessor?: boolean;
  dueDateAfter?: string;
  dueDateBefore?: string;
  createdAfter?: string;
  createdBefore?: string;
}

@Injectable()
export class QuestionnairesService {
  private readonly logger = new Logger(QuestionnairesService.name);

  constructor(private readonly questionnaireRepository: QuestionnaireRepository) {}

  async getQuestionnaires(
    context: SessionContext,
    filters?: GetQuestionnairesFilters,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    questionnaires: ContractsV1Questionnaires.QuestionnaireDTO[];
    total: number;
    stats: ContractsV1Questionnaires.QuestionnaireStatsDTO;
  }> {
    this.logger.debug(`Getting questionnaires for organization: ${context.organizationId}`);

    // Convert API filters to repository filters
    const repositoryFilters: QuestionnaireFilters = {};
    if (filters) {
      repositoryFilters.name = filters.search;
      repositoryFilters.status = filters.status;
      repositoryFilters.vendorId = filters.vendorId;
      repositoryFilters.scopeId = filters.scopeId;
      repositoryFilters.createdBy = filters.createdBy;
      repositoryFilters.assigned = filters.assigned;
      repositoryFilters.isAssessor = filters.isAssessor;
      repositoryFilters.dueDateAfter = filters.dueDateAfter;
      repositoryFilters.dueDateBefore = filters.dueDateBefore;
      repositoryFilters.createdAfter = filters.createdAfter;
      repositoryFilters.createdBefore = filters.createdBefore;
    }

    // Get questionnaires from repository
    const { questionnaires, total } = await this.questionnaireRepository.findByOrganization(
      context,
      repositoryFilters,
      page,
      limit,
    );

    // Transform questionnaires to DTOs
    const questionnaireDTOs = questionnaires.map((item) =>
      this.transformQuestionnaireToDTO(item.questionnaire, item.questions),
    );

    // Calculate stats
    const stats = this.calculateQuestionnaireStats(
      questionnaires.map((item) => item.questionnaire),
    );

    return {
      questionnaires: questionnaireDTOs,
      total,
      stats,
    };
  }

  async getQuestionnaireById(
    id: string,
    context: SessionContext,
  ): Promise<ContractsV1Questionnaires.QuestionnaireDTO> {
    const questionnaire = await this.questionnaireRepository.findById(id, context);
    if (!questionnaire) {
      throw new NotFoundException('Questionnaire not found');
    }
    return this.transformQuestionnaireToDTO(questionnaire);
  }

  async createQuestionnaire(
    data: ContractsV1Questionnaires.CreateQuestionnaireDTO,
    context: SessionContext,
  ): Promise<ContractsV1Questionnaires.QuestionnaireDTO> {
    this.logger.debug(`Creating questionnaire: ${data.name}`);

    // Convert base64 to Buffer for binary fields
    const createData = {
      ...data,
      dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
      originalFile: data.originalFile ? Buffer.from(data.originalFile, 'base64') : undefined,
      downloadFile: data.downloadFile ? Buffer.from(data.downloadFile, 'base64') : undefined,
    };

    const questionnaire = await this.questionnaireRepository.createQuestionnaire(
      createData,
      context,
    );
    if (!questionnaire) {
      throw new BadRequestException('Questionnaire not created');
    }
    return this.transformQuestionnaireToDTO(questionnaire);
  }

  async updateQuestionnaire(
    id: string,
    data: ContractsV1Questionnaires.UpdateQuestionnaireDTO,
    context: SessionContext,
  ): Promise<ContractsV1Questionnaires.QuestionnaireDTO> {
    this.logger.debug(`Updating questionnaire: ${id}`);

    // Convert base64 to Buffer for binary fields
    const updateData = {
      ...data,
      dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
      originalFile: data.originalFile ? Buffer.from(data.originalFile, 'base64') : undefined,
      downloadFile: data.downloadFile ? Buffer.from(data.downloadFile, 'base64') : undefined,
    };

    const questionnaire = await this.questionnaireRepository.updateQuestionnaire(
      id,
      updateData,
      context,
    );
    if (!questionnaire) {
      throw new NotFoundException('Questionnaire not found');
    }
    return this.transformQuestionnaireToDTO(questionnaire);
  }

  async deleteQuestionnaire(id: string, context: SessionContext): Promise<boolean> {
    this.logger.debug(`Deleting questionnaire: ${id}`);

    const result = await this.questionnaireRepository.deleteQuestionnaire(id, context);
    if (!result) {
      throw new NotFoundException('Questionnaire not found');
    }
    return result;
  }

  async getQuestionsByQuestionnaireId(
    questionnaireId: string,
    context: SessionContext,
  ): Promise<any[]> {
    this.logger.debug(`Getting questions for questionnaire: ${questionnaireId}`);

    const questions = await this.questionnaireRepository.getQuestionsByQuestionnaireId(
      questionnaireId,
      context,
    );

    return questions;
  }

  async updateQuestion(
    questionId: string,
    data: {
      answerYesNoNa?: 'Yes' | 'No' | 'NA' | null;
      answerDetail?: string | null;
      aiGeneratedAnswer?: string | null;
      status?: string;
    },
    context: SessionContext,
  ): Promise<Question> {
    this.logger.debug(`Updating question: ${questionId}`);

    const question = await this.questionnaireRepository.updateQuestion(questionId, data, context);

    if (!question) {
      throw new NotFoundException('Question not found');
    }

    return question;
  }

  private transformQuestionnaireToDTO(
    questionnaire: Questionnaire,
    questions: Question[] = [],
  ): ContractsV1Questionnaires.QuestionnaireDTO {
    console.log('[transformQuestionnaireToDTO] questions', questions.length);
    // Validate and map status to ensure it matches the expected enum
    const validStatuses: ContractsV1Questionnaires.QuestionnaireStatus[] = [
      'open',
      'in_progress',
      'in_review',
      'completed',
      'closed',
      'archived',
    ];

    const status =
      questionnaire.status &&
      validStatuses.includes(questionnaire.status as ContractsV1Questionnaires.QuestionnaireStatus)
        ? (questionnaire.status as ContractsV1Questionnaires.QuestionnaireStatus)
        : 'open';

    // Log invalid status values for debugging
    if (
      questionnaire.status &&
      !validStatuses.includes(questionnaire.status as ContractsV1Questionnaires.QuestionnaireStatus)
    ) {
      this.logger.warn(
        `Invalid questionnaire status '${questionnaire.status}' for questionnaire ${questionnaire.id}, defaulting to 'open'`,
      );
    }

    return {
      id: questionnaire.id,
      name: questionnaire.name,
      organizationId: questionnaire.organizationId,
      createdAt: questionnaire.createdAt.toISOString(),
      createdBy: questionnaire.createdBy,
      dueDate: questionnaire.dueDate?.toISOString(),
      vendorId: questionnaire.vendorId || undefined,
      status,
      assigned: questionnaire.assigned || [],
      originalFile: questionnaire.originalFile
        ? Buffer.from(questionnaire.originalFile).toString('base64')
        : undefined,
      downloadFile: questionnaire.downloadFile
        ? Buffer.from(questionnaire.downloadFile).toString('base64')
        : undefined,
      scopeId: questionnaire.scopeId || undefined,
      reference: questionnaire.reference || undefined,
      isAssessor: questionnaire.isAssessor || false,
      otherData: (questionnaire.otherData as Record<string, unknown>) || undefined,
      auditLog: (questionnaire.auditLog as Record<string, unknown>) || undefined,
      questions: questions.map((q) => ({
        id: q.id,
        questionnaireId: q.questionnaireId,
        question: q.question,
        answerType: '', // Not available in schema
        category: '', // Not available in schema
        answerDetail: q.answerDetail ?? undefined,
        answerYesNoNa: q.answerYesNoNa ?? undefined,
        aiGeneratedAnswer: q.aiGeneratedAnswer ?? undefined,
        status: q.status,
        assignedTo: q.assignedId ?? undefined,
      })),
    };
  }

  private calculateQuestionnaireStats(
    questionnaires: Questionnaire[],
  ): ContractsV1Questionnaires.QuestionnaireStatsDTO {
    const stats: ContractsV1Questionnaires.QuestionnaireStatsDTO = {
      total: questionnaires.length,
      byStatus: {},
      byVendor: {},
      assigned: 0,
      unassigned: 0,
      overdue: 0,
      recentCreations: 0,
    };

    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    questionnaires.forEach((questionnaire) => {
      // Count by status - only count valid statuses
      const validStatuses: ContractsV1Questionnaires.QuestionnaireStatus[] = [
        'open',
        'in_progress',
        'in_review',
        'completed',
        'closed',
        'archived',
      ];

      if (
        questionnaire.status &&
        validStatuses.includes(
          questionnaire.status as ContractsV1Questionnaires.QuestionnaireStatus,
        )
      ) {
        stats.byStatus![questionnaire.status] = (stats.byStatus![questionnaire.status] || 0) + 1;
      }

      // Count by vendor
      if (questionnaire.vendorId) {
        stats.byVendor![questionnaire.vendorId] =
          (stats.byVendor![questionnaire.vendorId] || 0) + 1;
      }

      // Count assigned/unassigned
      if (questionnaire.assigned && questionnaire.assigned.length > 0) {
        stats.assigned!++;
      } else {
        stats.unassigned!++;
      }

      // Count overdue questionnaires
      if (
        questionnaire.dueDate &&
        questionnaire.dueDate < now &&
        questionnaire.status !== 'completed' &&
        questionnaire.status !== 'in_review' &&
        questionnaire.status !== 'closed'
      ) {
        stats.overdue!++;
      }

      // Count recent creations (last 7 days)
      if (questionnaire.createdAt > sevenDaysAgo) {
        stats.recentCreations!++;
      }
    });

    return stats;
  }
}
