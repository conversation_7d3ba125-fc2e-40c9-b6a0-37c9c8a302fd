"use client";

import { useSearchParams, useRouter, usePathname } from "next/navigation";
import {
  Plus,
  Download,
  RefreshCw,
  Filter,
  BookOpen,
  Award,
  History,
  Users,
  UserPlus,
} from "lucide-react";
import { TabsSwitcher } from "../questionnaires/_components/tabs-switcher";
import { MyTrainingView } from "./_components/my-training-view";
import { ManageTrainingView } from "./_components/manage-training-view";
import type { TrainingViewType } from "./types/training";

export function TrainingsPageClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const activeTab = (searchParams.get("view") as TrainingViewType) || "me";

  function onTabChange(tabId: string) {
    // eslint-disable-next-line no-undef
    const params = new URLSearchParams(searchParams);
    params.set("view", tabId);
    router.push(`${pathname}?${params.toString()}`);
  }

  const tabs = [
    {
      id: "me",
      label: "My Trainings",
      icon: <BookOpen className="h-4 w-4" />,
    },
    {
      id: "manage",
      label: "Manage Trainings",
      icon: <Users className="h-4 w-4" />,
    },
  ];

  return (
    <div className="space-y-6">
      <TabsSwitcher tabs={tabs} activeTab={activeTab} onTabChange={onTabChange} />

      {activeTab === "me" ? (
        <>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <BookOpen className="h-4 w-4 mr-2" />
                Browse Catalog
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                <Award className="h-4 w-4 mr-2" />
                My Certificates
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                <History className="h-4 w-4 mr-2" />
                Learning History
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </button>
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </button>
            </div>
          </div>
          <MyTrainingView />
        </>
      ) : (
        <>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Plus className="h-4 w-4 mr-2" />
                Create Course
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                <Users className="h-4 w-4 mr-2" />
                Create Group
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                <UserPlus className="h-4 w-4 mr-2" />
                Assign Training
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </button>
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </button>
            </div>
          </div>
          <ManageTrainingView />
        </>
      )}
    </div>
  );
}
