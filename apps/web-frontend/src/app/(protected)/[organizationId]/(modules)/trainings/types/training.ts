export type TrainingViewType = "me" | "manage";

export type ContentType = "video" | "document" | "quiz" | "live-session";

export type CourseStatus = "not-started" | "in-progress" | "completed" | "overdue";

export type PolicyStatus = "pending" | "under-review" | "accepted" | "expired";

export interface Course {
  id: string;
  title: string;
  description: string;
  category: string;
  contentTypes: ContentType[];
  estimatedDuration: number; // in minutes
  progress?: number; // 0-100
  status: CourseStatus;
  dueDate?: string;
  score?: number;
  hasCertificate: boolean;
  enrolledUsers?: number; // for admin view
  totalUsers?: number; // for admin view
  averageScore?: number; // for admin view
  averageCompletionTime?: number; // for admin view
}

export interface Policy {
  id: string;
  title: string;
  version: string;
  lastUpdated: string;
  status: PolicyStatus;
  acceptanceRate?: number; // for admin view
  pendingCount?: number; // for admin view
  dueDate?: string;
}

export interface TrainingGroup {
  id: string;
  name: string;
  memberCount: number;
  assignedCourses: string[];
  completionRate: number;
  recentActivity?: string;
}

export interface Certification {
  id: string;
  name: string;
  issueDate: string;
  expirationDate?: string;
  courseId: string;
}

export interface LearnerMetrics {
  myProgress: number;
  pendingAssignments: number;
  completedThisMonth: number;
  myAverageScore: number;
  changeFromLastMonth?: number;
  changeFromLastWeek?: number;
}

export interface AdminMetrics {
  overallCompletion: number;
  activeLearners: number;
  averageScore: number;
  pendingAssignments: number;
  changeFromLastMonth?: number;
  changeFromLastWeek?: number;
  changeFromLastQuarter?: number;
}

export interface DepartmentPerformance {
  name: string;
  completionRate: number;
  averageScore: number;
  color: string;
}

export interface ActivityItem {
  id: string;
  message: string;
  details: string;
  timestamp: string;
  color: string;
}

export interface UpcomingDeadline {
  id: string;
  title: string;
  type: "course" | "policy" | "certification" | "live-session";
  dueDate: string;
  pendingCount?: number;
  color: string;
}
