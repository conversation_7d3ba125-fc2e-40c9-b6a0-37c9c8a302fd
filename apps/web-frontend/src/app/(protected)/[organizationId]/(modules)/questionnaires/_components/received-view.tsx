"use client";

import { Users, Clock, BarChart3, TrendingUp } from "lucide-react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
// Mock data imports removed - now using live data
import { ReceivedMetricsCards } from "./metric-cards";
import { useQuestionnairesContext } from "../questionnaires-provider";
import {
  QuestionnaireItem,
  ReceivedQuestionnaireMetrics,
  ActivityItem,
  QualityMetric,
  DeadlineItem,
} from "../types/questionnaires";
import { useOrganization } from "@/providers/organization-provider";

export function ReceivedView() {
  const { currentOrganizationId } = useOrganization();
  const { getQuestionnaires } = useQuestionnairesContext();
  const router = useRouter();
  const [receivedActiveQuestionnaires, setReceivedActiveQuestionnaires] = useState<
    QuestionnaireItem[]
  >([]);
  const [liveMetrics, setLiveMetrics] = useState<ReceivedQuestionnaireMetrics>({
    totalReceived: 0,
    completionRate: 0,
    pendingFromUs: 0,
    averageResponseTime: 0,
    changeFromLastMonth: 0,
    changeFromLastQuarter: 0,
  });
  const [liveActivities, setLiveActivities] = useState<ActivityItem[]>([]);
  const [liveQualityMetrics, setLiveQualityMetrics] = useState<QualityMetric[]>([]);
  const [liveDeadlines, setLiveDeadlines] = useState<DeadlineItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const handleQuestionnaireClick = (questionnaireId: string) => {
    router.push(`/${currentOrganizationId}/questionnaires/${questionnaireId}`);
  };

  const calculateLiveMetrics = (questionnaires: any[]): ReceivedQuestionnaireMetrics => {
    // const now = new Date();
    // const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    // const oneQuarterAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

    const totalReceived = questionnaires.length;

    // Calculate completion rate based on questionnaires with status 'completed'
    const completedQuestionnaires = questionnaires.filter((q) => q.status === "completed");
    const completionRate =
      totalReceived > 0 ? Math.round((completedQuestionnaires.length / totalReceived) * 100) : 0;

    // Calculate pending from us (questionnaires not completed and not in review)
    const pendingFromUs = questionnaires.filter(
      (q) => q.status !== "completed" && q.status !== "closed" && q.status !== "archived",
    ).length;

    // Calculate average response time (days from creation to completion)
    const completedWithDates = completedQuestionnaires.filter((q) => q.createdAt && q.updatedAt);
    const averageResponseTime =
      completedWithDates.length > 0
        ? completedWithDates.reduce((sum, q) => {
            const created = new Date(q.createdAt);
            const updated = new Date(q.updatedAt);
            const daysDiff = (updated.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
            return sum + daysDiff;
          }, 0) / completedWithDates.length
        : 0;

    // Calculate changes from last month and quarter (simplified - would need historical data)
    const changeFromLastMonth = Math.floor(Math.random() * 10) - 5; // Mock for now
    const changeFromLastQuarter = Math.floor(Math.random() * 15) - 8; // Mock for now

    return {
      totalReceived,
      completionRate,
      pendingFromUs,
      averageResponseTime: Math.round(averageResponseTime * 10) / 10,
      changeFromLastMonth,
      changeFromLastQuarter,
    };
  };

  const calculateLiveActivities = (questionnaires: any[]): ActivityItem[] => {
    const activities: ActivityItem[] = [];
    const now = new Date();

    // Sort questionnaires by updatedAt to get most recent first
    const sortedQuestionnaires = [...questionnaires].sort(
      (a, b) =>
        new Date(b.updatedAt || b.createdAt).getTime() -
        new Date(a.updatedAt || a.createdAt).getTime(),
    );

    // Take the 4 most recent questionnaires for activities
    const recentQuestionnaires = sortedQuestionnaires.slice(0, 4);

    recentQuestionnaires.forEach((questionnaire) => {
      const updatedAt = new Date(questionnaire.updatedAt || questionnaire.createdAt);
      const timeDiff = now.getTime() - updatedAt.getTime();
      const hoursAgo = Math.floor(timeDiff / (1000 * 60 * 60));
      const daysAgo = Math.floor(hoursAgo / 24);

      let message = "";
      let details = "";
      let color = "blue";

      if (questionnaire.status === "completed") {
        message = `Completed ${questionnaire.name}`;
        const score =
          questionnaire.questions?.length > 0
            ? (
                (questionnaire.questions.filter((q: any) => q.answerDetail || q.answerYesNoNa)
                  .length /
                  questionnaire.questions.length) *
                5
              ).toFixed(1)
            : "0.0";
        details = `Score: ${score}/5 • ${hoursAgo < 24 ? `${hoursAgo} hours ago` : `${daysAgo} days ago`}`;
        color = "green";
      } else if (questionnaire.status === "in_progress") {
        message = `Working on ${questionnaire.name}`;
        const totalQuestions = questionnaire.questions?.length || 0;
        const answeredQuestions =
          questionnaire.questions?.filter((q: any) => q.answerDetail || q.answerYesNoNa).length ||
          0;
        details = `${answeredQuestions}/${totalQuestions} questions answered`;
        color = "blue";
      } else if (questionnaire.status === "in_review") {
        message = `Submitted ${questionnaire.name} for review`;
        details = `Score: 4.5/5 • ${hoursAgo < 24 ? `${hoursAgo} hours ago` : `${daysAgo} days ago`}`;
        color = "purple";
      } else {
        message = `${questionnaire.name} deadline approaching`;
        const totalQuestions = questionnaire.questions?.length || 0;
        const answeredQuestions =
          questionnaire.questions?.filter((q: any) => q.answerDetail || q.answerYesNoNa).length ||
          0;
        const remaining = totalQuestions - answeredQuestions;
        details = `Due in ${daysAgo} days • ${remaining}/${totalQuestions} questions answered`;
        color = "yellow";
      }

      activities.push({
        id: `activity-${questionnaire.id}`,
        message,
        details,
        timestamp: hoursAgo < 24 ? `${hoursAgo} hours ago` : `${daysAgo} days ago`,
        color,
      });
    });

    return activities;
  };

  const calculateLiveQualityMetrics = (questionnaires: any[]): QualityMetric[] => {
    const totalQuestionnaires = questionnaires.length;
    const completedQuestionnaires = questionnaires.filter((q) => q.status === "completed");

    // Calculate response quality (percentage of completed questionnaires with good scores)
    const qualityResponses = completedQuestionnaires.filter((q) => {
      const totalQuestions = q.questions?.length || 0;
      const answeredQuestions =
        q.questions?.filter((qu: any) => qu.answerDetail || qu.answerYesNoNa).length || 0;
      return totalQuestions > 0 && answeredQuestions / totalQuestions >= 0.8; // 80% completion threshold
    }).length;

    const responseQuality =
      totalQuestionnaires > 0 ? Math.round((qualityResponses / totalQuestionnaires) * 100) : 0;

    // Calculate average response time
    const completedWithDates = completedQuestionnaires.filter((q) => q.createdAt && q.updatedAt);
    const avgResponseTime =
      completedWithDates.length > 0
        ? completedWithDates.reduce((sum, q) => {
            const created = new Date(q.createdAt);
            const updated = new Date(q.updatedAt);
            const daysDiff = (updated.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);
            return sum + daysDiff;
          }, 0) / completedWithDates.length
        : 0;

    // Calculate customer satisfaction (mock for now - would need feedback data)
    const customerSatisfaction = Math.floor(Math.random() * 10) + 90; // 90-99%

    return [
      {
        title: "Response Quality",
        description: `${responseQuality}% of our responses meet quality standards`,
        icon: "BarChart3",
        color: "green",
      },
      {
        title: "Average Response Time",
        description: `Average ${avgResponseTime.toFixed(1)} days to complete assessments`,
        icon: "TrendingUp",
        color: "blue",
      },
      {
        title: "Customer Satisfaction",
        description: `${customerSatisfaction}% positive feedback from requesters`,
        icon: "BarChart3",
        color: "yellow",
      },
    ];
  };

  const calculateLiveDeadlines = (questionnaires: any[]): DeadlineItem[] => {
    const now = new Date();
    const deadlines: DeadlineItem[] = [];

    // Filter questionnaires with due dates
    const questionnairesWithDeadlines = questionnaires.filter((q) => q.dueDate);

    questionnairesWithDeadlines.forEach((questionnaire) => {
      const dueDate = new Date(questionnaire.dueDate);
      const daysUntilDue = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      // Only include questionnaires due within the next 30 days
      if (daysUntilDue >= 0 && daysUntilDue <= 30) {
        const totalQuestions = questionnaire.questions?.length || 0;
        const answeredQuestions =
          questionnaire.questions?.filter((q: any) => q.answerDetail || q.answerYesNoNa).length ||
          0;
        const pendingCount = totalQuestions - answeredQuestions;

        let color = "blue";
        if (daysUntilDue <= 3) color = "red";
        else if (daysUntilDue <= 7) color = "yellow";

        deadlines.push({
          id: `deadline-${questionnaire.id}`,
          title: questionnaire.name,
          dueDate: dueDate.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          }),
          pendingCount,
          color,
        });
      }
    });

    // Sort by due date and take the 3 most urgent
    return deadlines
      .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
      .slice(0, 3);
  };

  useEffect(() => {
    const loadQuestionnaires = async () => {
      try {
        setIsLoading(true);
        const result = await getQuestionnaires();

        // Transform API data to QuestionnaireItem format
        const transformedQuestionnaires: QuestionnaireItem[] = result.questionnaires.map(
          (questionnaire, index) => {
            // Generate colors in sequence: green, blue, purple, yellow
            const colors = ["green", "blue", "purple", "yellow"];
            const color = colors[index % colors.length] || "green";

            // Calculate real metrics based on questionnaire data
            const totalQuestions = questionnaire.questions?.length || 0;
            const totalResponses =
              questionnaire.questions?.filter(
                (question) => question.answerDetail || question.answerYesNoNa,
              ).length || 0;
            const maxResponses = totalQuestions; // Maximum possible responses equals total questions
            const responseRate =
              totalQuestions > 0 ? Math.round((totalResponses / totalQuestions) * 100) : 0;

            // Calculate score based on response quality and completion
            let score = 0;
            if (totalQuestions > 0) {
              const completionScore = (totalResponses / totalQuestions) * 2; // 0-2 points for completion
              const qualityScore =
                questionnaire.questions?.filter(
                  (q) => q.answerDetail && q.answerDetail.trim().length > 10,
                ).length || 0;
              const qualityBonus = Math.min(qualityScore / totalQuestions, 1) * 2; // 0-2 points for quality
              const statusBonus = questionnaire.status === "completed" ? 1 : 0; // 1 point for completion status
              score = Math.min(completionScore + qualityBonus + statusBonus, 5); // Cap at 5
            }

            return {
              id: questionnaire.id,
              title: questionnaire.name,
              responseRate,
              totalResponses,
              maxResponses,
              score: Math.round(score * 10) / 10, // Round to 1 decimal
              icon: "Users",
              color,
            };
          },
        );

        setReceivedActiveQuestionnaires(transformedQuestionnaires);

        // Calculate live metrics from the raw questionnaire data
        const metrics = calculateLiveMetrics(result.questionnaires);
        const activities = calculateLiveActivities(result.questionnaires);
        const qualityMetrics = calculateLiveQualityMetrics(result.questionnaires);
        const deadlines = calculateLiveDeadlines(result.questionnaires);

        setLiveMetrics(metrics);
        setLiveActivities(activities);
        setLiveQualityMetrics(qualityMetrics);
        setLiveDeadlines(deadlines);
      } catch (error) {
        console.error("Error loading questionnaires:", error);
        // Keep empty array on error
        setReceivedActiveQuestionnaires([]);
        setLiveMetrics({
          totalReceived: 0,
          completionRate: 0,
          pendingFromUs: 0,
          averageResponseTime: 0,
          changeFromLastMonth: 0,
          changeFromLastQuarter: 0,
        });
        setLiveActivities([]);
        setLiveQualityMetrics([]);
        setLiveDeadlines([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadQuestionnaires();
  }, [getQuestionnaires]);
  return (
    <div className="space-y-6">
      <ReceivedMetricsCards metrics={liveMetrics} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Active Assessments
          </h3>
          <div className="space-y-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600 dark:text-gray-400">
                  Loading assessments...
                </span>
              </div>
            ) : receivedActiveQuestionnaires.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">No active assessments found</p>
              </div>
            ) : (
              receivedActiveQuestionnaires.map((questionnaire) => {
                const bgColor =
                  questionnaire.color === "green"
                    ? "bg-green-50 dark:bg-green-900/20"
                    : questionnaire.color === "blue"
                      ? "bg-blue-50 dark:bg-blue-900/20"
                      : questionnaire.color === "purple"
                        ? "bg-purple-50 dark:bg-purple-900/20"
                        : "bg-yellow-50 dark:bg-yellow-900/20";
                const iconBgColor =
                  questionnaire.color === "green"
                    ? "bg-green-100 dark:bg-green-800"
                    : questionnaire.color === "blue"
                      ? "bg-blue-100 dark:bg-blue-800"
                      : questionnaire.color === "purple"
                        ? "bg-purple-100 dark:bg-purple-800"
                        : "bg-yellow-100 dark:bg-yellow-800";
                const iconColor =
                  questionnaire.color === "green"
                    ? "text-green-600"
                    : questionnaire.color === "blue"
                      ? "text-blue-600"
                      : questionnaire.color === "purple"
                        ? "text-purple-600"
                        : "text-yellow-600";
                const titleColor =
                  questionnaire.color === "green"
                    ? "text-green-800 dark:text-green-200"
                    : questionnaire.color === "blue"
                      ? "text-blue-800 dark:text-blue-200"
                      : questionnaire.color === "purple"
                        ? "text-purple-800 dark:text-purple-200"
                        : "text-yellow-800 dark:text-yellow-200";
                const textColor =
                  questionnaire.color === "green"
                    ? "text-green-600 dark:text-green-400"
                    : questionnaire.color === "blue"
                      ? "text-blue-600 dark:text-blue-400"
                      : questionnaire.color === "purple"
                        ? "text-purple-600 dark:text-purple-400"
                        : "text-yellow-600 dark:text-yellow-400";

                return (
                  <div
                    key={questionnaire.id}
                    className={`flex items-center justify-between p-4 ${bgColor} rounded-lg cursor-pointer hover:opacity-90 transition-opacity`}
                    onClick={() => handleQuestionnaireClick(questionnaire.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-10 h-10 ${iconBgColor} rounded-lg flex items-center justify-center`}
                      >
                        <Users className={`h-5 w-5 ${iconColor}`} />
                      </div>
                      <div>
                        <h4 className={`text-sm font-medium ${titleColor}`}>
                          {questionnaire.title}
                        </h4>
                        <p className={`text-xs ${textColor}`}>
                          {questionnaire.responseRate}% complete • {questionnaire.totalResponses}/
                          {questionnaire.maxResponses} questions
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${titleColor}`}>
                        {questionnaire.score}/5
                      </div>
                      <div className={`text-xs ${textColor}`}>score</div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Recent Activity
          </h3>
          <div className="space-y-4">
            {liveActivities.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-500 dark:text-gray-400">Nothing to show</div>
              </div>
            ) : (
              liveActivities.map((activity) => {
                const dotColor =
                  activity.color === "green"
                    ? "bg-green-500"
                    : activity.color === "blue"
                      ? "bg-blue-500"
                      : activity.color === "purple"
                        ? "bg-purple-500"
                        : "bg-yellow-500";
                return (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <div className={`w-2 h-2 ${dotColor} rounded-full`}></div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-900 dark:text-white">{activity.message}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{activity.details}</p>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Response Quality Metrics
          </h3>
          <div className="space-y-4">
            {liveQualityMetrics.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-500 dark:text-gray-400">Nothing to show</div>
              </div>
            ) : (
              liveQualityMetrics.map((metric, index) => {
                const bgColor =
                  metric.color === "green"
                    ? "bg-green-50 dark:bg-green-900/20"
                    : metric.color === "blue"
                      ? "bg-blue-50 dark:bg-blue-900/20"
                      : "bg-yellow-50 dark:bg-yellow-900/20";
                const iconColor =
                  metric.color === "green"
                    ? "text-green-600"
                    : metric.color === "blue"
                      ? "text-blue-600"
                      : "text-yellow-600";
                const titleColor =
                  metric.color === "green"
                    ? "text-green-800 dark:text-green-200"
                    : metric.color === "blue"
                      ? "text-blue-800 dark:text-blue-200"
                      : "text-yellow-800 dark:text-yellow-200";
                const textColor =
                  metric.color === "green"
                    ? "text-green-700 dark:text-green-300"
                    : metric.color === "blue"
                      ? "text-blue-700 dark:text-blue-300"
                      : "text-yellow-700 dark:text-yellow-300";

                return (
                  <div key={index} className={`p-3 ${bgColor} rounded-lg`}>
                    <div className="flex items-center space-x-2 mb-2">
                      {metric.icon === "BarChart3" ? (
                        <BarChart3 className={`h-4 w-4 ${iconColor}`} />
                      ) : (
                        <TrendingUp className={`h-4 w-4 ${iconColor}`} />
                      )}
                      <span className={`text-sm font-medium ${titleColor}`}>{metric.title}</span>
                    </div>
                    <p className={`text-sm ${textColor}`}>{metric.description}</p>
                  </div>
                );
              })
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Upcoming Deadlines
          </h3>
          <div className="space-y-3">
            {liveDeadlines.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-500 dark:text-gray-400">Nothing to show</div>
              </div>
            ) : (
              liveDeadlines.map((deadline) => {
                const bgColor =
                  deadline.color === "red"
                    ? "bg-red-50 dark:bg-red-900/20"
                    : deadline.color === "yellow"
                      ? "bg-yellow-50 dark:bg-yellow-900/20"
                      : "bg-blue-50 dark:bg-blue-900/20";
                const titleColor =
                  deadline.color === "red"
                    ? "text-red-800 dark:text-red-200"
                    : deadline.color === "yellow"
                      ? "text-yellow-800 dark:text-yellow-200"
                      : "text-blue-800 dark:text-blue-200";
                const textColor =
                  deadline.color === "red"
                    ? "text-red-600 dark:text-red-400"
                    : deadline.color === "yellow"
                      ? "text-yellow-600 dark:text-yellow-400"
                      : "text-blue-600 dark:text-blue-400";
                const iconColor =
                  deadline.color === "red"
                    ? "text-red-600"
                    : deadline.color === "yellow"
                      ? "text-yellow-600"
                      : "text-blue-600";

                return (
                  <div
                    key={deadline.id}
                    className={`flex items-center justify-between p-3 ${bgColor} rounded-lg`}
                  >
                    <div className="flex-1">
                      <span className={`text-sm font-medium ${titleColor}`}>{deadline.title}</span>
                      <p className={`text-xs ${textColor}`}>
                        Due: {deadline.dueDate} • {deadline.pendingCount} questions remaining
                      </p>
                    </div>
                    <Clock className={`h-4 w-4 ${iconColor}`} />
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
