"use client";

import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { Plus, Send, Download, RefreshCw, Filter, Settings, FileQuestion } from "lucide-react";
import { TabsSwitcher } from "./_components/tabs-switcher";
import { ReceivedView } from "./_components/received-view";
import { SentView } from "./_components/sent-view";
// import { ViewRequestModal } from "./_components/view-request-modal";
import type { QuestionnaireType } from "./types/questionnaires";

export function QuestionnairesPageClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  // const [viewModalOpen, setViewModalOpen] = useState(false);
  const activeTab = (searchParams.get("type") as QuestionnaireType) || "sent";

  function onTabChange(tabId: string) {
    // eslint-disable-next-line no-undef
    const params = new URLSearchParams(searchParams);
    params.set("type", tabId);
    router.push(`${pathname}?${params.toString()}`);
  }

  const tabs = [
    {
      id: "received",
      label: "Received Questionnaires",
      icon: <FileQuestion className="h-4 w-4" />,
    },
    {
      id: "sent",
      label: "Sent Questionnaires",
      icon: <Send className="h-4 w-4" />,
    },
  ];

  // function onViewRequest() {
  //   setViewModalOpen(true);
  // }

  // function onCloseViewModal() {
  //   setViewModalOpen(false);
  // }

  // function onCompleteAssessment(questionnaireId: string) {
  //   // For now, just log - will implement in next phase
  //   console.log("Complete assessment for questionnaire:", questionnaireId);
  //   // TODO: Navigate to assessment page or open assessment modal
  //   // router.push(`${pathname}/${questionnaireId}/complete`);
  // }

  return (
    <div className="space-y-6">
      <TabsSwitcher tabs={tabs} activeTab={activeTab} onTabChange={onTabChange} />

      {activeTab === "received" ? (
        <>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* <button
                onClick={onViewRequest}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Request
              </button> */}
              {/* <button className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                <Plus className="h-4 w-4 mr-2" />
                Complete Assessment
              </button> */}
              {/* <button className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                <Download className="h-4 w-4 mr-2" />
                Export Our Responses
              </button> */}
            </div>
            <div className="flex items-center space-x-2">
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </button>
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </button>
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </button>
            </div>
          </div>
          <ReceivedView />
        </>
      ) : (
        <>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Plus className="h-4 w-4 mr-2" />
                Create Questionnaire
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                <Send className="h-4 w-4 mr-2" />
                Send Reminders
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </button>
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </button>
              <button className="inline-flex items-center px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </button>
            </div>
          </div>
          <SentView />
        </>
      )}

      {/* View Request Modal */}
      {/* <ViewRequestModal
        isOpen={viewModalOpen}
        onClose={onCloseViewModal}
        onCompleteAssessment={onCompleteAssessment}
      /> */}
    </div>
  );
}
