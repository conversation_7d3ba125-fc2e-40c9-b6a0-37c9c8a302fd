"use client";

import React, { createContext, useCallback, useContext, useMemo, useState } from "react";
import { ContractsV1Questionnaires } from "@askinfosec/types";
import {
  createQuestionnaire as createQuestionnaireAction,
  CreateQuestionnaireParams,
  CreateQuestionnaireResponse,
} from "./_actions/create-questionnaire";
import {
  updateQuestionnaire as updateQuestionnaireAction,
  UpdateQuestionnaireParams,
} from "./_actions/update-questionnaire";
import {
  deleteQuestionnaires as deleteQuestionnairesAction,
  DeleteQuestionnairesResponse,
} from "./_actions/delete-questionnaires";
import { downloadQuestionnaire as downloadQuestionnaireAction } from "./_actions/download-questionnaire";
import { getAllQuestionnaires as getAllQuestionnairesAction } from "./_actions/get-all-questionnaires";

// Questionnaire upload state interfaces
interface UploadQuestionnaire {
  file: File;
  id: string;
  status: "pending" | "uploading" | "success" | "error";
  progress?: number;
  error?: string;
}

interface QuestionnairesContextValue {
  organizationId: string;
  // Central refresh signal for readers (e.g., table) to react and refetch
  refreshVersion: number;
  requestRefresh: () => void;

  // Local add/open signals for table to react without ref coupling
  lastAdded?: ContractsV1Questionnaires.QuestionnaireDTO;
  lastAddedVersion: number;
  addQuestionnaireLocal: (questionnaire: ContractsV1Questionnaires.QuestionnaireDTO) => void;

  openQuestionnaireId?: string;
  openVersion: number;
  openQuestionnaireLocal: (id: string) => void;

  // File upload state for questionnaire files
  selectedFiles: UploadQuestionnaire[];
  setSelectedFiles: (files: UploadQuestionnaire[]) => void;
  addSelectedFiles: (files: File[]) => void;
  removeSelectedFile: (fileId: string) => void;
  clearSelectedFiles: () => void;

  // CRUD surface
  create: (params: CreateQuestionnaireParams) => Promise<CreateQuestionnaireResponse>;
  update: (
    params: UpdateQuestionnaireParams,
  ) => Promise<ContractsV1Questionnaires.QuestionnaireDTO>;
  remove: (questionnaireIds: string[]) => Promise<DeleteQuestionnairesResponse>;
  download: (
    questionnaireId: string,
    fileType?: "original" | "download",
  ) => Promise<{ success: boolean; data?: { file: string; filename: string }; error?: string }>;
  getQuestionnaires: (
    filters?: GetQuestionnairesFilters,
  ) => Promise<ContractsV1Questionnaires.GetQuestionnairesResponse>;
  getQuestionnaireById: (id: string) => Promise<ContractsV1Questionnaires.QuestionnaireDTO>;
}

interface GetQuestionnairesFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: ContractsV1Questionnaires.QuestionnaireStatus;
  vendorId?: string;
  scopeId?: string;
  createdBy?: string;
  dueDateFrom?: string;
  dueDateTo?: string;
}

const QuestionnairesContext = createContext<QuestionnairesContextValue | undefined>(undefined);

interface QuestionnairesProviderProps {
  organizationId: string;
  children: React.ReactNode;
}

function generateTempId(nameHint: string) {
  if (typeof crypto !== "undefined" && typeof crypto.randomUUID === "function") {
    return crypto.randomUUID();
  }
  // Safe fallback when crypto.randomUUID is unavailable
  return `${nameHint}-${Date.now()}-${Math.random().toString(36).slice(2, 10)}`;
}

export function QuestionnairesProvider({ organizationId, children }: QuestionnairesProviderProps) {
  const [refreshVersion, setRefreshVersion] = useState(0);
  const [lastAdded, setLastAdded] = useState<
    ContractsV1Questionnaires.QuestionnaireDTO | undefined
  >(undefined);
  const [lastAddedVersion, setLastAddedVersion] = useState(0);
  const [openQuestionnaireId, setOpenQuestionnaireId] = useState<string | undefined>(undefined);
  const [openVersion, setOpenVersion] = useState(0);
  const [selectedFiles, setSelectedFiles] = useState<UploadQuestionnaire[]>([]);

  const requestRefresh = useCallback(() => {
    setRefreshVersion((v) => v + 1);
  }, []);

  const addQuestionnaireLocal = useCallback(
    (questionnaire: ContractsV1Questionnaires.QuestionnaireDTO) => {
      setLastAdded(questionnaire);
      setLastAddedVersion((v) => v + 1);
    },
    [],
  );

  const openQuestionnaireLocal = useCallback((id: string) => {
    setOpenQuestionnaireId(id);
    setOpenVersion((v) => v + 1);
  }, []);

  // File upload state management
  const addSelectedFiles = useCallback((files: File[]) => {
    const newUploadFiles: UploadQuestionnaire[] = files.map((file) => ({
      file,
      id: generateTempId(file.name),
      status: "pending" as const,
    }));
    setSelectedFiles((prev) => [...prev, ...newUploadFiles]);
  }, []);

  const removeSelectedFile = useCallback((fileId: string) => {
    setSelectedFiles((prev) => prev.filter((f) => f.id !== fileId));
  }, []);

  const clearSelectedFiles = useCallback(() => {
    setSelectedFiles([]);
  }, []);

  // API functions
  const create = useCallback<QuestionnairesContextValue["create"]>(
    async (params) => {
      const result = await createQuestionnaireAction({ ...params, organizationId });
      if (result.success && result.questionnaire) {
        addQuestionnaireLocal(result.questionnaire);
        openQuestionnaireLocal(result.questionnaire.id);
        requestRefresh();
      }
      return result;
    },
    [addQuestionnaireLocal, openQuestionnaireLocal, requestRefresh],
  );

  const update = useCallback<QuestionnairesContextValue["update"]>(
    async (params) => {
      const result = await updateQuestionnaireAction({ ...params, organizationId });
      requestRefresh();
      return result;
    },
    [requestRefresh],
  );

  const remove = useCallback<QuestionnairesContextValue["remove"]>(
    async (questionnaireIds) => {
      const result = await deleteQuestionnairesAction({ organizationId, questionnaireIds });
      if (result.success) {
        requestRefresh();
      }
      return result;
    },
    [organizationId, requestRefresh],
  );

  const download = useCallback<QuestionnairesContextValue["download"]>(
    async (questionnaireId, fileType = "original") => {
      return downloadQuestionnaireAction(organizationId, questionnaireId, fileType);
    },
    [organizationId],
  );

  const getQuestionnaires = useCallback<QuestionnairesContextValue["getQuestionnaires"]>(
    async (filters = {}) => {
      const result = await getAllQuestionnairesAction(organizationId, filters);
      console.log("result", result);
      if (result.error) {
        return {
          questionnaires: [],
          total: 0,
          page: 1,
          limit: 20,
          hasMore: false,
          stats: {
            total: 0,
            byStatus: {},
            byVendor: {},
            assigned: 0,
            unassigned: 0,
            overdue: 0,
            recentCreations: 0,
          },
        };
      }

      return {
        questionnaires: result.questionnaires,
        total: result.total,
        page: result.page,
        limit: result.limit,
        hasMore: result.hasMore,
        stats: result.stats || {
          total: 0,
          byStatus: {},
          byVendor: {},
          assigned: 0,
          unassigned: 0,
          overdue: 0,
          recentCreations: 0,
        },
      };
    },
    [organizationId],
  );

  const getQuestionnaireById = useCallback<QuestionnairesContextValue["getQuestionnaireById"]>(
    async (id) => {
      const result = await getAllQuestionnairesAction(organizationId, { limit: 1000 });

      if (result.error) {
        throw new Error("Failed to fetch questionnaire");
      }

      const questionnaire = result.questionnaires.find((q) => q.id === id);
      if (!questionnaire) {
        // Instead of throwing an error, we'll return a special response
        // that the modal can handle gracefully
        throw new Error(
          "The requested questionnaire could not be found. It may have been deleted or you may not have permission to view it.",
        );
      }

      return questionnaire;
    },
    [organizationId],
  );

  const value = useMemo<QuestionnairesContextValue>(
    () => ({
      organizationId,
      refreshVersion,
      requestRefresh,
      lastAdded,
      lastAddedVersion,
      addQuestionnaireLocal,
      openQuestionnaireId,
      openVersion,
      openQuestionnaireLocal,
      selectedFiles,
      setSelectedFiles,
      addSelectedFiles,
      removeSelectedFile,
      clearSelectedFiles,
      create,
      update,
      remove,
      download,
      getQuestionnaires,
      getQuestionnaireById,
    }),
    [
      organizationId,
      refreshVersion,
      requestRefresh,
      lastAdded,
      lastAddedVersion,
      addQuestionnaireLocal,
      openQuestionnaireId,
      openVersion,
      openQuestionnaireLocal,
      selectedFiles,
      addSelectedFiles,
      removeSelectedFile,
      clearSelectedFiles,
      create,
      update,
      remove,
      download,
      getQuestionnaires,
      getQuestionnaireById,
    ],
  );

  return <QuestionnairesContext.Provider value={value}>{children}</QuestionnairesContext.Provider>;
}

export function useQuestionnairesContext(): QuestionnairesContextValue {
  const ctx = useContext(QuestionnairesContext);
  if (!ctx) {
    throw new Error("useQuestionnairesContext must be used within a QuestionnairesProvider");
  }
  return ctx;
}
