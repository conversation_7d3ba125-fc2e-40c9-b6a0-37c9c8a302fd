"use client";

import { cn } from "@/lib/utils";

export interface Tab {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

interface TabsSwitcherProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export function TabsSwitcher({ tabs, activeTab, onTabChange, className }: TabsSwitcherProps) {
  function onKeyDown(event: React.KeyboardEvent, tabId: string) {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      onTabChange(tabId);
    }
  }

  return (
    <div
      className={cn(
        "inline-flex items-center rounded-lg bg-gray-100 dark:bg-gray-800 p-1 gap-1",
        className,
      )}
      role="tablist"
      aria-label="Questionnaire types"
    >
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        return (
          <button
            key={tab.id}
            type="button"
            role="tab"
            aria-selected={isActive}
            aria-controls={`panel-${tab.id}`}
            id={`tab-${tab.id}`}
            tabIndex={isActive ? 0 : -1}
            onClick={() => onTabChange(tab.id)}
            onKeyDown={(e) => onKeyDown(e, tab.id)}
            className={cn(
              "inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-all",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
              isActive
                ? "bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm"
                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200",
            )}
          >
            {tab.icon && <span className="mr-2">{tab.icon}</span>}
            {tab.label}
          </button>
        );
      })}
    </div>
  );
}
