"use client";

import React, { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { LogOut, ChevronDown, User, BookOpen } from "lucide-react";
import { UserProfileDropdownProps } from "@/types/dashboard";
import { cn } from "@/lib/utils";
import { useOrganization } from "@/providers/organization-provider";
import { Separator } from "@/components/ui/separator";

export const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({ user, onSignOut }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { currentOrganizationId } = useOrganization();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  if (!user) {
    return null;
  }

  const initials = user.name
    .split(" ")
    .map((name) => name.charAt(0))
    .join("")
    .toUpperCase()
    .slice(0, 2);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-2 py-2 rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors duration-200"
        aria-label={`User menu for ${user.name}`}
        aria-expanded={isOpen}
        aria-haspopup="true"
        type="button"
      >
        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">
          {initials}
        </div>
        <div className="text-left hidden md:block">
          <p className="text-sm font-medium text-foreground truncate max-w-32">{user.name}</p>
          <p className="text-xs text-muted-foreground truncate max-w-32">{user.email}</p>
        </div>
        <ChevronDown
          className={cn(
            "h-4 w-4 text-muted-foreground transition-transform duration-200 dark:text-muted-foreground",
            isOpen ? "rotate-180" : "",
          )}
          aria-hidden="true"
        />
      </button>

      {isOpen && (
        <div
          className="absolute right-0 top-full mt-2 w-48 bg-card rounded-lg shadow-lg border border-border z-[100] dark:bg-card dark:border-border dark:shadow-2xl"
          role="menu"
        >
          <div className="p-2">
            <Link
              href={`/${currentOrganizationId}/my-account`}
              onClick={() => setIsOpen(false)}
              className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors duration-150"
            >
              <User className="h-4 w-4" aria-hidden="true" />
              <span>My Account</span>
            </Link>
            <Link
              href={`/${currentOrganizationId}/trainings?view=me`}
              onClick={() => setIsOpen(false)}
              className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground rounded-lg transition-colors duration-150"
            >
              <BookOpen className="h-4 w-4" aria-hidden="true" />
              <span>My Trainings</span>
            </Link>
          </div>
          <Separator />
          <div className="p-2">
            <button
              onClick={onSignOut}
              className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-destructive hover:bg-destructive/10 hover:text-destructive rounded-lg transition-colors duration-150 dark:text-red-400 dark:hover:bg-destructive/20 dark:hover:text-red-300 dark:focus-visible:bg-destructive/20 dark:focus-visible:text-red-300"
              aria-label="Sign out of your account"
              type="button"
              role="menuitem"
            >
              <LogOut className="h-4 w-4" aria-hidden="true" />
              <span>Sign out</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
