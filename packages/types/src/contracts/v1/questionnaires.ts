import { z } from "zod";

// Question Schema
export const QuestionSchema = z.object({
  id: z.string(),
  questionnaireId: z.string(),
  question: z.string(),
  answerType: z.string(),
  category: z.string(),
  answerDetail: z.string().optional(),
  answerYesNoNa: z.string().optional(),
  aiGeneratedAnswer: z.string().optional(),
  status: z.string(),
  assignedTo: z.string().optional(),
});

// Questionnaire Status Schema
export const QuestionnaireStatusSchema = z.enum([
  "open",
  "in_progress",
  "in_review",
  "completed",
  "closed",
  "archived"
]);

// Questionnaire Schema
export const QuestionnaireSchema = z.object({
  id: z.string(),
  name: z.string(),
  organizationId: z.string(),
  createdAt: z.string(),
  createdBy: z.string(),
  dueDate: z.string().optional(),
  vendorId: z.string().optional(),
  status: QuestionnaireStatusSchema.optional().default("open"),
  assigned: z.array(z.string()).optional().default([]),
  originalFile: z.string().optional(), // Base64 encoded binary data
  downloadFile: z.string().optional(), // Base64 encoded binary data
  scopeId: z.string().optional(),
  reference: z.string().optional(),
  isAssessor: z.boolean().optional().default(false),
  otherData: z.any().optional(),
  auditLog: z.any().optional(),
  questions: z.array(QuestionSchema).optional().default([]),
});

// Questionnaire Stats Schema
export const QuestionnaireStatsSchema = z.object({
  total: z.number().optional().default(0),
  byStatus: z.record(z.string(), z.number()).optional().default({}),
  byVendor: z.record(z.string(), z.number()).optional().default({}),
  assigned: z.number().optional().default(0),
  unassigned: z.number().optional().default(0),
  overdue: z.number().optional().default(0),
  recentCreations: z.number().optional().default(0),
});

// Create Questionnaire Schema
export const CreateQuestionnaireSchema = z.object({
  name: z.string().min(1, "Name is required"),
  dueDate: z.string().optional(),
  vendorId: z.string().optional(),
  status: QuestionnaireStatusSchema.optional(),
  assigned: z.array(z.string()).optional(),
  originalFile: z.string().optional(), // Base64 encoded
  downloadFile: z.string().optional(), // Base64 encoded
  scopeId: z.string().optional(),
  reference: z.string().optional(),
  isAssessor: z.boolean().optional(),
  otherData: z.any().optional(),
});

// Update Questionnaire Schema
export const UpdateQuestionnaireSchema = z.object({
  name: z.string().min(1).optional(),
  dueDate: z.string().optional(),
  vendorId: z.string().optional(),
  status: QuestionnaireStatusSchema.optional(),
  assigned: z.array(z.string()).optional(),
  originalFile: z.string().optional(), // Base64 encoded
  downloadFile: z.string().optional(), // Base64 encoded
  scopeId: z.string().optional(),
  reference: z.string().optional(),
  isAssessor: z.boolean().optional(),
  otherData: z.any().optional(),
});

// Get Questionnaires Request Schema
export const GetQuestionnairesRequestSchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(20),
  sortBy: z.string().optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
  filters: z.object({
    search: z.string().optional(),
    status: QuestionnaireStatusSchema.array().optional(),
    vendorId: z.string().array().optional(),
    scopeId: z.string().array().optional(),
    createdBy: z.string().array().optional(),
    assigned: z.string().array().optional(),
    isAssessor: z.boolean().optional(),
    dueDateAfter: z.string().optional(),
    dueDateBefore: z.string().optional(),
    createdAfter: z.string().optional(),
    createdBefore: z.string().optional(),
  }).optional(),
});

// Get Questionnaires Response Schema
export const GetQuestionnairesResponseSchema = z.object({
  questionnaires: z.array(QuestionnaireSchema).optional().default([]),
  stats: QuestionnaireStatsSchema.optional().default(QuestionnaireStatsSchema.parse({})),
  total: z.number().optional().default(0),
  page: z.number().optional().default(1),
  limit: z.number().optional().default(20),
  hasMore: z.boolean().optional().default(false),
});

// Delete Questionnaire Response Schema
export const DeleteQuestionnaireResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
});

// Types
export type QuestionnaireDTO = z.infer<typeof QuestionnaireSchema>;
export type QuestionnaireStatus = z.infer<typeof QuestionnaireStatusSchema>;
export type QuestionnaireStatsDTO = z.infer<typeof QuestionnaireStatsSchema>;
export type CreateQuestionnaireDTO = z.infer<typeof CreateQuestionnaireSchema>;
export type UpdateQuestionnaireDTO = z.infer<typeof UpdateQuestionnaireSchema>;
export type GetQuestionnairesRequest = z.infer<typeof GetQuestionnairesRequestSchema>;
export type GetQuestionnairesResponse = z.infer<typeof GetQuestionnairesResponseSchema>;
export type DeleteQuestionnaireResponse = z.infer<typeof DeleteQuestionnaireResponseSchema>;
export type QuestionDTO = z.infer<typeof QuestionSchema>;

