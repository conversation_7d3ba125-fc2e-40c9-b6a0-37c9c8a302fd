import { eq, and, like, desc, count, inArray, gte, lte } from 'drizzle-orm';
import { BaseRepository, SessionContext, IdGenerationConfig } from '../lib/base-repository.js';
import { 
  questionnaires,
  type Questionnaire, 
  type NewQuestionnaire,
  questions,
  type Question
} from '../schemas/schema.js';

export interface QuestionnaireFilters {
  ids?: string[];
  name?: string;
  status?: string[];
  vendorId?: string[];
  scopeId?: string[];
  createdBy?: string[];
  assigned?: string[];
  isAssessor?: boolean;
  dueDateAfter?: string;
  dueDateBefore?: string;
  createdAfter?: string;
  createdBefore?: string;
}

export interface CreateQuestionnaireData {
  name: string;
  dueDate?: Date;
  vendorId?: string;
  status?: string;
  assigned?: string[];
  originalFile?: Buffer;
  downloadFile?: Buffer;
  scopeId?: string;
  reference?: string;
  isAssessor?: boolean;
  otherData?: any;
}

export interface UpdateQuestionnaireData {
  name?: string;
  dueDate?: Date;
  vendorId?: string;
  status?: string;
  assigned?: string[];
  originalFile?: Buffer;
  downloadFile?: Buffer;
  scopeId?: string;
  reference?: string;
  isAssessor?: boolean;
  otherData?: any;
}

export interface QuestionnaireWithQuestions {
  questionnaire: Questionnaire;
  questions: Question[];
}

export class QuestionnaireRepository extends BaseRepository {
  protected readonly idConfig: IdGenerationConfig = {
    usePrefix: false,
    prefix: 'questionnaire'
  };
  
  private validateContext(context: SessionContext, requiredFields: (keyof SessionContext)[] = ['organizationId']): void {
    if (!context) {
      throw new Error('Session context is required');
    }
    
    for (const field of requiredFields) {
      if (!context[field]) {
        throw new Error(`${field} is required in session context`);
      }
    }
  }

  async findById(id: string, context: SessionContext): Promise<Questionnaire | null> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select()
        .from(questionnaires)
        .where(eq(questionnaires.id, id))
        .limit(1);
      
      return result[0] || null;
    }, context, 'findById');
  }

  async findByOrganization(
    context: SessionContext,
    filters?: QuestionnaireFilters,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ questionnaires: QuestionnaireWithQuestions[]; total: number }> {
    this.validateContext(context);
    const offset = (page - 1) * limit;
    
    return this.executeQueryWithSession(async (tx) => {
      // Build where conditions
      const whereConditions = [
        eq(questionnaires.organizationId, context.organizationId as string)
      ];
      
      if (filters?.ids) {
        whereConditions.push(inArray(questionnaires.id, filters.ids));
      }
      
      if (filters?.name) {
        const nameLower = filters.name.toLowerCase();
        whereConditions.push(like(questionnaires.name, `%${nameLower}%`));
      }
      
      if (filters?.status && filters.status.length > 0) {
        whereConditions.push(inArray(questionnaires.status, filters.status));
      }
      
      if (filters?.vendorId && filters.vendorId.length > 0) {
        whereConditions.push(inArray(questionnaires.vendorId, filters.vendorId));
      }
      
      if (filters?.scopeId && filters.scopeId.length > 0) {
        whereConditions.push(inArray(questionnaires.scopeId, filters.scopeId));
      }
      
      if (filters?.createdBy && filters.createdBy.length > 0) {
        whereConditions.push(inArray(questionnaires.createdBy, filters.createdBy));
      }
      
      if (filters?.isAssessor !== undefined) {
        whereConditions.push(eq(questionnaires.isAssessor, filters.isAssessor));
      }
      
      if (filters?.dueDateAfter) {
        whereConditions.push(gte(questionnaires.dueDate, new Date(filters.dueDateAfter)));
      }
      
      if (filters?.dueDateBefore) {
        whereConditions.push(lte(questionnaires.dueDate, new Date(filters.dueDateBefore)));
      }
      
      if (filters?.createdAfter) {
        whereConditions.push(gte(questionnaires.createdAt, new Date(filters.createdAfter)));
      }
      
      if (filters?.createdBefore) {
        whereConditions.push(lte(questionnaires.createdAt, new Date(filters.createdBefore)));
      }

      // Get total count
      const countResult = await tx
        .select({ count: count() })
        .from(questionnaires)
        .where(and(...whereConditions));

      const total = countResult[0]?.count || 0;

      // First get paginated questionnaires
      const questionnaireResults = await tx
        .select()
        .from(questionnaires)
        .where(and(...whereConditions))
        .orderBy(desc(questionnaires.createdAt))
        .limit(limit)
        .offset(offset);

      // Get all questions for these questionnaires
      const questionnaireIds = questionnaireResults.map(q => q.id);
      const allQuestions = questionnaireIds.length > 0 ? await tx
        .select()
        .from(questions)
        .where(and(
          inArray(questions.questionnaireId, questionnaireIds),
          eq(questions.organizationId, context.organizationId as string)
        )) : [];

      // Group questions by questionnaire ID
      const questionsByQuestionnaire = new Map<string, Question[]>();
      for (const question of allQuestions) {
        if (!questionsByQuestionnaire.has(question.questionnaireId)) {
          questionsByQuestionnaire.set(question.questionnaireId, []);
        }
        questionsByQuestionnaire.get(question.questionnaireId)!.push(question);
      }

      // Combine questionnaires with their questions
      const result = questionnaireResults.map(questionnaire => ({
        questionnaire,
        questions: questionsByQuestionnaire.get(questionnaire.id) || []
      }));

      console.log(`[Repository] Total questionnaires: ${result.length}`);
      let totalQuestionsFound = 0;
      
      for (const item of result) {
        totalQuestionsFound += item.questions.length;
      }
      
      console.log(`[Repository] Total questions found: ${totalQuestionsFound}`);
      console.log(`[Repository] Questionnaires with questions: ${result.length}`);
      
      // Apply in-memory filtering for array fields if specified
      let questionnairesWithQuestions = result;
      
      if (filters?.assigned && filters.assigned.length > 0) {
        questionnairesWithQuestions = questionnairesWithQuestions.filter(item => 
          item.questionnaire.assigned?.some(id => filters.assigned!.includes(id))
        );
      }

      return {
        questionnaires: questionnairesWithQuestions,
        total: questionnairesWithQuestions.length !== result.length ? questionnairesWithQuestions.length : total,
      };
    }, context, 'findByOrganization');
  }

  async findByVendor(vendorId: string, context: SessionContext): Promise<Questionnaire[]> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select()
        .from(questionnaires)
        .where(and(
          eq(questionnaires.vendorId, vendorId),
          eq(questionnaires.organizationId, context.organizationId as string)
        ))
        .orderBy(desc(questionnaires.createdAt));

      return result;
    }, context, 'findByVendor');
  }

  async findByStatus(status: string, context: SessionContext): Promise<Questionnaire[]> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select()
        .from(questionnaires)
        .where(and(
          eq(questionnaires.status, status),
          eq(questionnaires.organizationId, context.organizationId as string)
        ))
        .orderBy(desc(questionnaires.createdAt));

      return result;
    }, context, 'findByStatus');
  }

  async searchQuestionnaires(searchTerm: string, context: SessionContext): Promise<Questionnaire[]> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const searchLower = searchTerm.toLowerCase();
      const result = await tx
        .select()
        .from(questionnaires)
        .where(and(
          eq(questionnaires.organizationId, context.organizationId as string),
          like(questionnaires.name, `%${searchLower}%`)
        ))
        .orderBy(desc(questionnaires.createdAt));

      return result;
    }, context, 'searchQuestionnaires');
  }

  async getQuestionnaireCount(context: SessionContext): Promise<number> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select({ count: count() })
        .from(questionnaires)
        .where(eq(questionnaires.organizationId, context.organizationId as string));

      return result[0]?.count || 0;
    }, context, 'getQuestionnaireCount');
  }

  async getQuestionnaireCountByStatus(status: string, context: SessionContext): Promise<number> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select({ count: count() })
        .from(questionnaires)
        .where(and(
          eq(questionnaires.status, status),
          eq(questionnaires.organizationId, context.organizationId as string)
        ));

      return result[0]?.count || 0;
    }, context, 'getQuestionnaireCountByStatus');
  }

  async createQuestionnaire(
    data: CreateQuestionnaireData, 
    context: SessionContext
  ): Promise<Questionnaire> {
    this.validateContext(context, ['organizationId', 'userId']);
    const createdBy = context.userId;
    const createdAt = new Date();
    
    return this.executeQueryWithSession(async (tx) => {
      const id = this.generateId();
      
      const result = await tx.insert(questionnaires).values({
        id,
        name: data.name,
        organizationId: context.organizationId as string,
        createdBy: createdBy as string,
        createdAt,
        dueDate: data.dueDate || null,
        vendorId: data.vendorId || null,
        status: data.status || 'open',
        assigned: data.assigned || null,
        originalFile: data.originalFile || null,
        downloadFile: data.downloadFile || null,
        scopeId: data.scopeId || null,
        reference: data.reference || null,
        isAssessor: data.isAssessor || false,
        otherData: data.otherData || null,
        auditLog: null,
      }).returning();
      
      return result[0];
    }, context, 'createQuestionnaire');
  }

  async updateQuestionnaire(
    questionnaireId: string, 
    data: UpdateQuestionnaireData, 
    context: SessionContext
  ): Promise<Questionnaire | null> {
    this.validateContext(context, ['organizationId', 'userId']);
    
    return this.executeQueryWithSession(async (tx) => {
      const updatePayload: Record<string, unknown> = {};

      // Copy defined fields from the DTO
      for (const [key, value] of Object.entries(data)) {
        if (value !== undefined) {
          updatePayload[key] = value;
        }
      }

      const result = await tx
        .update(questionnaires)
        .set(updatePayload)
        .where(and(
          eq(questionnaires.id, questionnaireId),
          eq(questionnaires.organizationId, context.organizationId as string)
        ))
        .returning();
  
      return result[0] || null;
    }, context, 'updateQuestionnaire');
  }

  async deleteQuestionnaire(questionnaireId: string, context: SessionContext): Promise<boolean> {
    this.validateContext(context, ['organizationId', 'userId']);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .delete(questionnaires)
        .where(and(
          eq(questionnaires.id, questionnaireId),
          eq(questionnaires.organizationId, context.organizationId as string)
        ))
        .returning();
      
      return result.length > 0;
    }, context, 'deleteQuestionnaire');
  }

  async deleteQuestionnaires(questionnaireIds: string[], context: SessionContext): Promise<boolean> {
    this.validateContext(context, ['organizationId', 'userId']);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .delete(questionnaires)
        .where(and(
          inArray(questionnaires.id, questionnaireIds),
          eq(questionnaires.organizationId, context.organizationId as string)
        ))
        .returning();
      
      return result.length > 0;
    }, context, 'deleteQuestionnaires');
  }

  // QUESTIONS
  async getQuestionsByQuestionnaireId(questionnaireId: string, context: SessionContext): Promise<Question[]> {
    this.validateContext(context);

    return this.executeQueryWithSession(async (tx) => {
      const result = await tx.select().from(questions).where(eq(questions.questionnaireId, questionnaireId));
      return result;
    }, context, 'getQuestionsByQuestionnaireId');
  }

  async updateQuestion(
    questionId: string,
    data: {
      answerYesNoNa?: 'Yes' | 'No' | 'NA' | null;
      answerDetail?: string | null;
      aiGeneratedAnswer?: string | null;
      status?: string;
    },
    context: SessionContext
  ): Promise<Question | null> {
    this.validateContext(context, ['organizationId']);

    return this.executeQueryWithSession(async (tx) => {
      const updateData: any = {};

      if (data.answerYesNoNa !== undefined) updateData.answerYesNoNa = data.answerYesNoNa;
      if (data.answerDetail !== undefined) updateData.answerDetail = data.answerDetail;
      if (data.aiGeneratedAnswer !== undefined) updateData.aiGeneratedAnswer = data.aiGeneratedAnswer;
      if (data.status !== undefined) updateData.status = data.status;

      const result = await tx
        .update(questions)
        .set(updateData)
        .where(
          and(
            eq(questions.id, questionId),
            eq(questions.organizationId, context.organizationId as string)
          )
        )
        .returning();

      return result[0] || null;
    }, context, 'updateQuestion');
  }
}

